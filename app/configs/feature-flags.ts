/**
 * Feature Flags Configuration
 *
 * This file contains boolean flags to control feature rollouts and A/B testing.
 * These are compile-time constants, not environment variables.
 */

// =============================================================================
// CHAT SYSTEM FLAGS
// =============================================================================

/**
 * Enable Chat v2 System
 *
 * When true: Uses ChatTabContentV2 with improved API architecture
 * When false: Uses ChatTabContent (v1) with legacy architecture
 *
 * Chat v2 Benefits:
 * - API handles message history retrieval internally
 * - Reduced payload size for long conversations
 * - Better separation of concerns
 * - Proper system message handling for context
 * - AI SDK v5 native support with tool call display
 */
export const ENABLE_CHAT_V2 = true

/**
 * Enable Chat v2 Enhanced Persistence
 *
 * When true: Uses optimized persistence with conversation history reconstruction
 * When false: Uses simplified persistence (current v2 implementation)
 */
export const ENABLE_CHAT_V2_ENHANCED_PERSISTENCE = true

// =============================================================================
// AI PANE FLAGS
// =============================================================================

/**
 * Enable AI Pane Chat v2
 *
 * Controls which chat system is used when starting new AI Pane chats
 */
export const ENABLE_AI_PANE_CHAT_V2 = true

// =============================================================================
// RESEARCH SYSTEM FLAGS
// =============================================================================

/**
 * Enable Two-Stage Research System
 *
 * When true: Uses two-stage research generation (template assessment + custom flow)
 * When false: Uses existing single-template research approach
 *
 * Two-Stage Research Benefits:
 * - Template assessment analyzes research question suitability
 * - Custom flow generation for specialized research scenarios
 * - Better research quality for diverse question types
 * - Maintains backward compatibility with existing template
 */
export const ENABLE_TWO_STAGE_RESEARCH = true

// =============================================================================
// DEVELOPMENT FLAGS
// =============================================================================

/**
 * Enable Debug Logging for Chat Systems
 *
 * When true: Enables detailed console logging for chat operations
 * When false: Uses production logging levels
 */
export const ENABLE_CHAT_DEBUG_LOGGING = true // Temporarily enabled for debugging

/**
 * Enable Performance Monitoring
 *
 * When true: Tracks and logs performance metrics for chat operations
 * When false: Skips performance tracking
 */
export const ENABLE_CHAT_PERFORMANCE_MONITORING = true

// =============================================================================
// FEATURE FLAG UTILITIES
// =============================================================================

/**
 * Get the current chat system version based on feature flags
 */
export const getChatSystemVersion = (): 'v1' | 'v2' => {
  return ENABLE_CHAT_V2 ? 'v2' : 'v1'
}

/**
 * Get the API endpoint for the current chat system
 */
export const getChatApiEndpoint = (): string => {
  return ENABLE_CHAT_V2 ? '/api/aipane/chat-v2' : '/api/aipane/chat'
}

/**
 * Check if enhanced features are enabled for the current chat system
 */
export const isChatEnhancedFeaturesEnabled = (): boolean => {
  return ENABLE_CHAT_V2 && ENABLE_CHAT_V2_ENHANCED_PERSISTENCE
}

/**
 * Get feature flag status for debugging
 */
export const getFeatureFlagStatus = () => {
  return {
    chatV2: ENABLE_CHAT_V2,
    chatV2EnhancedPersistence: ENABLE_CHAT_V2_ENHANCED_PERSISTENCE,
    aiPaneChatV2: ENABLE_AI_PANE_CHAT_V2,
    twoStageResearch: ENABLE_TWO_STAGE_RESEARCH,
    debugLogging: ENABLE_CHAT_DEBUG_LOGGING,
    performanceMonitoring: ENABLE_CHAT_PERFORMANCE_MONITORING,
    currentChatSystem: getChatSystemVersion(),
    currentApiEndpoint: getChatApiEndpoint(),
  }
}

// =============================================================================
// FEATURE FLAG VALIDATION
// =============================================================================

/**
 * Validate feature flag configuration at startup
 * This helps catch configuration issues early
 */
export const validateFeatureFlags = (): {
  valid: boolean
  errors: string[]
} => {
  const errors: string[] = []

  // Validate chat system flags
  if (ENABLE_CHAT_V2_ENHANCED_PERSISTENCE && !ENABLE_CHAT_V2) {
    errors.push(
      'ENABLE_CHAT_V2_ENHANCED_PERSISTENCE requires ENABLE_CHAT_V2 to be true'
    )
  }

  if (ENABLE_AI_PANE_CHAT_V2 && !ENABLE_CHAT_V2) {
    errors.push('ENABLE_AI_PANE_CHAT_V2 requires ENABLE_CHAT_V2 to be true')
  }

  return {
    valid: errors.length === 0,
    errors,
  }
}

// Log feature flag status in development
if (ENABLE_CHAT_DEBUG_LOGGING) {
  console.log(
    '🚩 [Feature Flags] Current configuration:',
    getFeatureFlagStatus()
  )

  const validation = validateFeatureFlags()
  if (!validation.valid) {
    console.error('❌ [Feature Flags] Configuration errors:', validation.errors)
  } else {
    console.log('✅ [Feature Flags] Configuration is valid')
  }
}
