import { describe, it, expect } from '@jest/globals'
import { LLM_MODEL_CONFIG, type ModelConfig } from '../llm-models'
import { SubscriptionTier } from '@prisma/client'

describe('LLM Models Configuration', () => {
  describe('GPT-5 Model Provider API Validation', () => {
    it('should ensure all models with "gpt-5" keyword use azure_eastus2 provider', () => {
      const violations: Array<{
        tier: string
        route: string
        model: string
        providerApi: string | undefined
      }> = []

      // Iterate through all subscription tiers and routes
      Object.entries(LLM_MODEL_CONFIG).forEach(([tier, routes]) => {
        Object.entries(routes).forEach(([route, config]) => {
          const modelConfig = config as ModelConfig

          // Check if model name contains "gpt-5"
          if (modelConfig.model.toLowerCase().includes('gpt-5')) {
            // Verify it uses azure_eastus2 provider API
            if (modelConfig.model_provider_api !== 'azure_eastus2') {
              violations.push({
                tier,
                route,
                model: modelConfig.model,
                providerApi: modelConfig.model_provider_api,
              })
            }
          }
        })
      })

      // If there are violations, create a detailed error message
      if (violations.length > 0) {
        const violationDetails = violations
          .map(
            v =>
              `  - ${v.tier}.${v.route}: "${v.model}" uses "${v.providerApi}" (should be "azure_eastus2")`
          )
          .join('\n')

        throw new Error(
          `Found ${violations.length} GPT-5 model(s) not using azure_eastus2 provider:\n${violationDetails}`
        )
      }

      // Test passes if no violations found
      expect(violations).toHaveLength(0)
    })

    it('should validate that azure_eastus2 is only used for gpt-5 models', () => {
      const violations: Array<{
        tier: string
        route: string
        model: string
      }> = []

      // Iterate through all subscription tiers and routes
      Object.entries(LLM_MODEL_CONFIG).forEach(([tier, routes]) => {
        Object.entries(routes).forEach(([route, config]) => {
          const modelConfig = config as ModelConfig

          // Check if model uses azure_eastus2 but doesn't contain "gpt-5"
          if (
            modelConfig.model_provider_api === 'azure_eastus2' &&
            !modelConfig.model.toLowerCase().includes('gpt-5')
          ) {
            violations.push({
              tier,
              route,
              model: modelConfig.model,
            })
          }
        })
      })

      // If there are violations, create a detailed error message
      if (violations.length > 0) {
        const violationDetails = violations
          .map(
            v =>
              `  - ${v.tier}.${v.route}: "${v.model}" uses azure_eastus2 but is not a GPT-5 model`
          )
          .join('\n')

        throw new Error(
          `Found ${violations.length} non-GPT-5 model(s) using azure_eastus2 provider:\n${violationDetails}`
        )
      }

      // Test passes if no violations found
      expect(violations).toHaveLength(0)
    })

    it('should validate that all Azure models have a valid provider API', () => {
      const validProviderApis = ['azure_standard', 'azure_eastus2']
      const violations: Array<{
        tier: string
        route: string
        model: string
        providerApi: string | undefined
      }> = []

      // Iterate through all subscription tiers and routes
      Object.entries(LLM_MODEL_CONFIG).forEach(([tier, routes]) => {
        Object.entries(routes).forEach(([route, config]) => {
          const modelConfig = config as ModelConfig

          // Check Azure models
          if (modelConfig.model_provider === 'azure') {
            const providerApi =
              modelConfig.model_provider_api || 'azure_standard'

            if (!validProviderApis.includes(providerApi)) {
              violations.push({
                tier,
                route,
                model: modelConfig.model,
                providerApi: modelConfig.model_provider_api,
              })
            }
          }
        })
      })

      // If there are violations, create a detailed error message
      if (violations.length > 0) {
        const violationDetails = violations
          .map(
            v =>
              `  - ${v.tier}.${v.route}: "${v.model}" uses invalid provider API "${v.providerApi}"`
          )
          .join('\n')

        throw new Error(
          `Found ${violations.length} Azure model(s) with invalid provider API:\n${violationDetails}\nValid options: ${validProviderApis.join(', ')}`
        )
      }

      // Test passes if no violations found
      expect(violations).toHaveLength(0)
    })
  })

  describe('Model Configuration Structure', () => {
    it('should have all required subscription tiers', () => {
      const requiredTiers = Object.values(SubscriptionTier)
      const configTiers = Object.keys(LLM_MODEL_CONFIG)

      requiredTiers.forEach(tier => {
        expect(configTiers).toContain(tier)
      })
    })

    it('should have consistent route coverage across tiers', () => {
      const tierRoutes = Object.entries(LLM_MODEL_CONFIG).map(
        ([tier, routes]) => ({
          tier,
          routes: Object.keys(routes).sort(),
        })
      )

      // Compare each tier's routes with the first tier
      const baseRoutes = tierRoutes[0]?.routes || []

      tierRoutes.forEach(({ routes }) => {
        expect(routes).toEqual(baseRoutes)
      })
    })
  })

  describe('Environment Variable Validation', () => {
    // Save original environment variables
    const originalEnv = { ...process.env }

    // Helper function to test provider creation with missing env vars
    const testProviderWithMissingEnv = async (
      missingVars: string[],
      providerType: 'azure_standard' | 'azure_eastus2' | 'openai'
    ) => {
      // Clear the specified environment variables
      missingVars.forEach(varName => {
        delete process.env[varName]
      })

      try {
        // Dynamically import to avoid module caching issues
        const { getAzureProvider, getModelFromProvider } = await import(
          '../../libs/model-provider-factory'
        )

        if (providerType === 'azure_standard') {
          expect(() => getAzureProvider('azure_standard')).toThrow()
        } else if (providerType === 'azure_eastus2') {
          expect(() => getAzureProvider('azure_eastus2')).toThrow()
        } else if (providerType === 'openai') {
          expect(() => getModelFromProvider('gpt-4', 'openai')).toThrow()
        }
      } finally {
        // Restore environment variables
        process.env = { ...originalEnv }
      }
    }

    it('should throw error when Azure Standard environment variables are missing', async () => {
      await testProviderWithMissingEnv(
        ['AZURE_RESOURCE_NAME'],
        'azure_standard'
      )
      await testProviderWithMissingEnv(['AZURE_API_KEY'], 'azure_standard')
    })

    it('should throw error when Azure East US 2 environment variables are missing', async () => {
      await testProviderWithMissingEnv(
        ['AZURE_EASTUS2_RESOURCE_NAME'],
        'azure_eastus2'
      )
      await testProviderWithMissingEnv(
        ['AZURE_EASTUS2_API_KEY'],
        'azure_eastus2'
      )
    })

    it('should throw error when OpenAI environment variable is missing', async () => {
      await testProviderWithMissingEnv(['OPENAI_API_KEY'], 'openai')
    })

    it('should provide descriptive error messages for missing environment variables', async () => {
      const originalAzureKey = process.env.AZURE_API_KEY
      delete process.env.AZURE_API_KEY

      try {
        const { getAzureProvider } = await import(
          '../../libs/model-provider-factory'
        )
        expect(() => getAzureProvider('azure_standard')).toThrow(
          /Missing Azure Standard environment variables/
        )
      } finally {
        if (originalAzureKey) {
          process.env.AZURE_API_KEY = originalAzureKey
        }
      }
    })

    it('should validate that required environment variables are present in current environment', () => {
      // This test checks if the current environment has the required variables
      // It will only run if the environment variables are expected to be present
      const azureStandardRequired = ['AZURE_RESOURCE_NAME', 'AZURE_API_KEY']
      const azureEastUs2Required = [
        'AZURE_EASTUS2_RESOURCE_NAME',
        'AZURE_EASTUS2_API_KEY',
      ]

      // Check if any Azure Standard variables are present
      const hasAzureStandard = azureStandardRequired.some(
        varName => process.env[varName]
      )
      if (hasAzureStandard) {
        azureStandardRequired.forEach(varName => {
          expect(process.env[varName]).toBeDefined()
        })
      }

      // Check if any Azure East US 2 variables are present
      const hasAzureEastUs2 = azureEastUs2Required.some(
        varName => process.env[varName]
      )
      if (hasAzureEastUs2) {
        azureEastUs2Required.forEach(varName => {
          expect(process.env[varName]).toBeDefined()
        })
      }

      // OpenAI is optional - only test if present
      if (process.env.OPENAI_API_KEY) {
        expect(process.env.OPENAI_API_KEY).toBeDefined()
      }
    })
  })

  describe('Title Generation Configuration', () => {
    it('should have title_generation route in all subscription tiers', () => {
      Object.entries(LLM_MODEL_CONFIG).forEach(([_tier, routes]) => {
        expect(routes).toHaveProperty('title_generation')
        expect(routes.title_generation).toBeDefined()
      })
    })

    it('should use gpt-4.1-mini for title generation across all tiers', () => {
      Object.entries(LLM_MODEL_CONFIG).forEach(([_tier, routes]) => {
        const titleConfig = routes.title_generation as ModelConfig
        expect(titleConfig.model).toBe('gpt-4.1-mini')
        expect(titleConfig.model_provider).toBe('azure')
        expect(titleConfig.temperature).toBe(0.3)
        expect(titleConfig.maxOutputTokens).toBe(100)
      })
    })
  })
})
