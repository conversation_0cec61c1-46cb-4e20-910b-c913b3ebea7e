import { streamText, UIMessage, convertToModelMessages, stepCountIs } from 'ai'
import { openai } from '@ai-sdk/openai'
import { NextRequest } from 'next/server'
// import { writeFile } from 'fs/promises'
import path from 'path'

// Allow streaming responses up to 30 seconds
export const maxDuration = 30

/**
 * Chat with Search Demo API Route
 *
 * Demonstrates OpenAI GPT-5-nano with native web search capabilities
 * using Vercel AI SDK v5 and OpenAI's web_search_preview tool.
 *
 * Features:
 * - OpenAI GPT-5-nano model with medium reasoning effort
 * - Native web search tool with medium search context
 * - Real-time streaming of reasoning steps and search results
 * - Complete response logging and sample data export
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    console.log('🚀 [Chat Search Demo] Request received:', {
      hasMessage: !!body.message,
      hasMessages: !!body.messages,
      context: body.context,
    })

    // Extract message(s) from request (AI SDK v5 pattern) - minimal version
    let messages: UIMessage[] = []
    if (body.message) {
      messages = [body.message]
      console.log(
        '📝 [Chat Search Demo] Processing single message:',
        body.message.parts?.[0]?.text?.slice(0, 100)
      )
    } else if (Array.isArray(body.messages)) {
      messages = body.messages
      console.log(
        '📝 [Chat Search Demo] Processing messages array:',
        messages.length
      )
    } else {
      throw new Error('No message or messages provided')
    }

    // Convert to model messages format
    const modelMessages = convertToModelMessages(messages)
    console.log(
      '🔄 [Chat Search Demo] Converted to model messages:',
      modelMessages.length
    )

    // Track streaming chunks for export
    const chunks: any[] = []

    // Create the streaming response with OpenAI GPT-5-nano and native web search
    const result = streamText({
      model: openai.responses('gpt-5-nano'), // Use responses API for web search support
      messages: modelMessages,
      tools: {
        // OpenAI's native web search tool
        web_search_preview: openai.tools.webSearchPreview({
          // Configure search context and reasoning
          searchContextSize: 'medium',
        }),
      },
      // Let the model autonomously choose tools (required by gpt-5-nano for web_search_preview)
      toolChoice: 'auto',
      maxOutputTokens: 4000,
      temperature: 0.7,
      // Enable multi-step tool calling; allow enough steps for a final answer
      stopWhen: stepCountIs(8),
      // Configure reasoning effort and summaries
      providerOptions: {
        openai: {
          reasoningEffort: 'low', // 'minimal', 'low', 'medium', 'high'
          reasoningSummary: 'auto', // 'auto' for condensed or 'detailed' for comprehensive
          textVerbosity: 'low', // 'low', 'medium', 'high'
          // Enable response storage for debugging
          store: true,
          metadata: {
            demo: 'chat-with-search',
            timestamp: new Date().toISOString(),
          },
        },
      },
      system: `You are a helpful AI assistant with access to real-time web search capabilities.

When users ask questions, you should:
1. Use web search to find current, accurate information
2. Provide comprehensive answers based on your search results
3. Include proper citations and sources
4. Show your reasoning process when helpful

Always search for the most recent and relevant information to provide accurate, up-to-date responses.`,
      onChunk: async chunk => {
        chunks.push(chunk)
      },
    })

    console.log('🌊 [Chat Search Demo] Starting stream response')

    // Return UIMessage stream (sample export disabled by request)
    // To re-enable export, uncomment the onFinish block below to write UI messages to sample_data.txt
    return result.toUIMessageStreamResponse({
      originalMessages: messages,
      // onFinish: async ({ messages: uiMessages }) => {
      //   try {
      //     const payload = {
      //       messages: Array.isArray(uiMessages) ? uiMessages : [],
      //     }
      //     const dst = path.join(
      //       process.cwd(),
      //       'app',
      //       '(conv)',
      //       'dragTree',
      //       'chat-with-search-demo',
      //       'sample_data.txt'
      //     )
      //     await writeFile(dst, JSON.stringify(payload, null, 2), 'utf8')
      //     console.log('💾 [Chat Search Demo] UI sample saved:', dst)
      //   } catch (exportError) {
      //     try {
      //       await writeFile(
      //         '/tmp/sample_data.txt',
      //         JSON.stringify({ messages: [] }, null, 2),
      //         'utf8'
      //       )
      //     } catch {}
      //   }
      // },
    })
  } catch (error) {
    console.error('💥 [Chat Search Demo] API Error:', error)

    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    )
  }
}

export async function GET() {
  try {
    const filePath = path.join(
      process.cwd(),
      'app',
      '(conv)',
      'dragTree',
      'chat-with-search-demo',
      'sample_data.txt'
    )
    const raw = await import('fs/promises').then(fs =>
      fs.readFile(filePath, 'utf8')
    )
    const parsed = JSON.parse(raw)
    const messages = Array.isArray(parsed?.messages) ? parsed.messages : []
    return new Response(
      JSON.stringify({ messages, meta: parsed?.meta ?? null }),
      { status: 200, headers: { 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Sample not available' }), {
      status: 404,
      headers: { 'Content-Type': 'application/json' },
    })
  }
}
